<template>
  <view class="complete-page">
    <NavBar :title="'冥想完成'" />
    
    <view class="complete-content">
      <!-- 完成祝贺 -->
      <view class="congratulation-section">
        <view class="success-icon">✨</view>
        <text class="congrat-title">恭喜完成冥想！</text>
        <text class="congrat-subtitle">{{ meditationTitle }}</text>
      </view>

      <!-- 统计信息 -->
      <view class="stats-section">
        <view class="stats-card">
          <view class="stat-item">
            <text class="stat-number">{{ formatTime(meditationDuration) }}</text>
            <text class="stat-label">冥想时长</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{ completedCount }}</text>
            <text class="stat-label">累计完成</text>
          </view>
          <view class="stat-divider"></view>
          <view class="stat-item">
            <text class="stat-number">{{ streakDays }}</text>
            <text class="stat-label">连续天数</text>
          </view>
        </view>
      </view>

      <!-- 感悟记录 -->
      <view class="reflection-section">
        <text class="section-title">记录此刻的感受</text>
        <textarea 
          class="reflection-input"
          v-model="reflection"
          placeholder="分享你的冥想感悟..."
          maxlength="200"
        />
        <text class="char-count">{{ reflection.length }}/200</text>
      </view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <view class="btn-secondary" @click="goBack">
          <text class="btn-text">返回</text>
        </view>
        <view class="btn-primary" @click="saveAndContinue">
          <text class="btn-text">保存并继续</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import NavBar from '@/components/NavBar.vue'

const meditationId = ref('')
const meditationTitle = ref('冥想练习')
const meditationDuration = ref(0)
const completedCount = ref(1)
const streakDays = ref(1)
const reflection = ref('')

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}分${secs}秒`
}

// 返回上一页
const goBack = () => {
  uni.navigateBack({
    delta: 2 // 返回到冥想详情页面
  })
}

// 保存并继续
const saveAndContinue = () => {
  // 这里可以保存用户的感悟到服务器
  if (reflection.value.trim()) {
    // TODO: 调用API保存感悟
    console.log('保存感悟:', reflection.value)
  }
  
  uni.showToast({
    title: '保存成功',
    icon: 'success'
  })
  
  setTimeout(() => {
    // 返回到首页或探索页面
    uni.switchTab({
      url: '/pages/plan/plan'
    })
  }, 1500)
}

onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options
  
  if (options && options.id) {
    meditationId.value = options.id
    // 根据id设置标题（这里可以从服务器获取）
    const titleMap: Record<string, string> = {
      '101': '呼吸觉察冥想',
      '201': '深度放松冥想',
      '301': '单点专注冥想'
    }
    meditationTitle.value = titleMap[options.id] || '冥想练习'
  }
  
  // 模拟获取冥想时长（实际应该从上一页传递或从服务器获取）
  meditationDuration.value = 600 // 10分钟
  
  // 模拟获取用户统计数据
  completedCount.value = Math.floor(Math.random() * 50) + 1
  streakDays.value = Math.floor(Math.random() * 10) + 1
})
</script>

<style scoped>
.complete-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F9F0 0%, #F7FAFC 40%, #FFFFFF 100%);
  display: flex;
  flex-direction: column;
  position: relative;
}

.complete-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 50%, rgba(240, 249, 240, 0.03) 100%);
  pointer-events: none;
  z-index: 0;
}

.complete-content {
  flex: 1;
  padding: 176rpx 24rpx 32rpx;
  position: relative;
  z-index: 1;
}

/* 祝贺区域 */
.congratulation-section {
  text-align: center;
  margin-bottom: 60rpx;
}

.success-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

.congrat-title {
  display: block;
  font-size: 48rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.congrat-subtitle {
  font-size: 28rpx;
  color: #7fb069;
  font-weight: 500;
}

/* 统计区域 */
.stats-section {
  margin-bottom: 60rpx;
}

.stats-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 40rpx 20rpx;
  box-shadow: 0 8rpx 32rpx rgba(127, 176, 105, 0.08), 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: relative;
  overflow: hidden;
}

.stats-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
}

.stat-number {
  font-size: 36rpx;
  font-weight: 700;
  color: #7fb069;
  margin-bottom: 8rpx;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

.stat-label {
  font-size: 24rpx;
  color: #718096;
  font-weight: 500;
}

.stat-divider {
  width: 2rpx;
  height: 60rpx;
  background: linear-gradient(180deg, rgba(127, 176, 105, 0.2) 0%, rgba(127, 176, 105, 0.1) 100%);
}

/* 感悟记录区域 */
.reflection-section {
  margin-bottom: 80rpx;
}

.section-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 24rpx;
}

.reflection-input {
  width: 100%;
  min-height: 200rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 16rpx;
  padding: 24rpx;
  font-size: 28rpx;
  color: #2d3748;
  line-height: 1.6;
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(127, 176, 105, 0.06);
  resize: none;
}

.reflection-input::placeholder {
  color: #a0aec0;
}

.char-count {
  display: block;
  text-align: right;
  font-size: 24rpx;
  color: #a0aec0;
  margin-top: 12rpx;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  gap: 24rpx;
}

.btn-secondary,
.btn-primary {
  flex: 1;
  height: 88rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.btn-secondary {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(160, 174, 192, 0.3);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.btn-primary {
  background: linear-gradient(135deg, #7FB069 0%, #A8D08D 100%);
  box-shadow: 0 8rpx 24rpx rgba(127, 176, 105, 0.3), 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn-secondary:active {
  transform: scale(0.98);
  background: rgba(247, 250, 252, 0.8);
}

.btn-primary:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 16rpx rgba(127, 176, 105, 0.4), 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
}

.btn-secondary .btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #4a5568;
}

.btn-primary .btn-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
}
</style>
