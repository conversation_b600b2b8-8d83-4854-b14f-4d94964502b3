// 计划相关API接口
import { api, type ApiResponse, buildQueryString } from '@/utils/request'
import type { MeditationContent } from './meditation'

// 计划项接口
export interface PlanItem {
  id: string
  plan_id: string
  meditation_id: string
  seq: number
  completed: boolean
  completed_at?: string | null
  meditation: MeditationContent
}

// 每日计划接口
export interface DailyPlan {
  id: string
  user_id: string
  plan_date: string
  created_at: string
  items: PlanItem[]
}

// 历史计划项接口
export interface HistoryPlanItem {
  id: string
  user_id: string
  meditation_id: string
  completed_at: string
  meditation: MeditationContent
}

// 计划统计接口
export interface PlanStats {
  total_plans: number
  total_items: number
  completed_items: number
  completion_rate: string
}

// 分页响应接口
export interface PaginationResponse<T> {
  total: number
  pageNum: number
  pageSize: number
  pages: number
  items: T[]
}

// 获取每日计划的参数
export interface GetDailyPlansParams {
  date: string // 格式: YYYY-MM-DD
}

// 添加到计划的参数 - 一次性计划
export interface AddOncePlanParams {
  meditation_id: number
  plan_type: 'once'
  start_date: string // 格式: YYYY-MM-DD
  start_time: string // 格式: HH:mm
}

// 添加到计划的参数 - 循环计划
export interface AddRecurringPlanParams {
  meditation_id: number
  plan_type: 'recurring'
  cycle_duration: 'week' | 'month' | 'quarter' // 循环天数：一周、一个月、三个月
  cycle_frequency: 'daily' | 'every_two_days' // 循环频率：每天、两天一次
  start_date: string // 格式: YYYY-MM-DD
  start_time: string // 格式: HH:mm
}

// 添加到计划的参数联合类型
export type AddToPlanParams = AddOncePlanParams | AddRecurringPlanParams

// 获取历史计划的参数
export interface GetPlanHistoryParams {
  pageNum?: number
  pageSize?: number
  start_date?: string // 格式: YYYY-MM-DD
  end_date?: string   // 格式: YYYY-MM-DD
}

// 获取计划统计的参数
export interface GetPlanStatsParams {
  start_date?: string // 格式: YYYY-MM-DD
  end_date?: string   // 格式: YYYY-MM-DD
}

// 计划API类
export class PlanApi {
  // 获取每日计划（包括前三天今天和后三天）
  static async getDailyPlans(params: GetDailyPlansParams): Promise<ApiResponse<DailyPlan[]>> {
    const queryString = buildQueryString({
      date: params.date
    })

    return api.get<DailyPlan[]>(`/api/plan/daily?${queryString}`)
  }

  // 添加到计划
  static async addToPlan(params: AddToPlanParams): Promise<ApiResponse<any>> {
    return api.post('/api/plan/add', params)
  }

  // 从计划删除
  static async removeFromPlan(itemId: string): Promise<ApiResponse<any>> {
    return api.delete(`/api/plan/item/${itemId}`)
  }

  // 获取历史计划
  static async getPlanHistory(params: GetPlanHistoryParams = {}): Promise<ApiResponse<PaginationResponse<HistoryPlanItem>>> {
    const queryString = buildQueryString({
      pageNum: params.pageNum,
      pageSize: params.pageSize,
      start_date: params.start_date,
      end_date: params.end_date
    })

    const url = `/api/plan/history${queryString ? `?${queryString}` : ''}`
    return api.get<PaginationResponse<HistoryPlanItem>>(url)
  }

  // 获取计划统计
  static async getPlanStats(params: GetPlanStatsParams = {}): Promise<ApiResponse<PlanStats>> {
    const queryString = buildQueryString({
      start_date: params.start_date,
      end_date: params.end_date
    })

    const url = `/api/plan/stats${queryString ? `?${queryString}` : ''}`
    return api.get<PlanStats>(url)
  }
}
