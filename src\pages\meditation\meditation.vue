<template>
  <view class="meditation-page">
    <NavBar :title="meditationTitle" />

    <view class="meditation-content">
      <!-- 冥想信息 -->
      <view class="meditation-info">
        <text class="meditation-text">{{ meditationTitle }}</text>
        <text class="meditation-subtitle">开始你的冥想之旅</text>
      </view>

      <!-- 音频播放器 -->
      <view class="audio-player">
        <!-- 播放按钮 -->
        <view class="play-button" @click="togglePlay">
          <text class="play-icon">{{ isPlaying ? '⏸' : '▶' }}</text>
        </view>

        <!-- 进度条和时间 -->
        <view class="progress-section">
          <view class="time-display">
            <text class="current-time">{{ formatTime(currentTime) }}</text>
            <text class="total-time">{{ formatTime(duration) }}</text>
          </view>

          <!-- 调试信息 -->
          <view class="debug-info" style="font-size: 20rpx; color: #999; margin-bottom: 10rpx;">
            <text>播放状态: {{ isPlaying ? '播放中' : '暂停' }}</text>
            <text> | 进度: {{ progressPercent.toFixed(1) }}%</text>
            <text> | 时长: {{ duration.toFixed(1) }}s</text>
            <text> | 当前时间: {{ currentTime.toFixed(1) }}s</text>
          </view>

          <!-- 路径测试按钮 -->
          <view class="debug-buttons" style="display: flex; gap: 10rpx; margin-bottom: 10rpx; flex-wrap: wrap;">
            <button size="mini" @click="testNextPath">测试下一路径</button>
            <button size="mini" @click="checkAudioStatus">检查状态</button>
            <button size="mini" @click="forcePlay">强制播放</button>
            <button size="mini" @click="resetAudio">重置音频</button>
            <button size="mini" @click="tryBackgroundAudio">背景音频</button>
            <button size="mini" @click="showAudioTips">播放提示</button>
          </view>

          <view class="progress-bar" @click="onProgressClick">
            <view class="progress-track">
              <view class="progress-fill" :style="{ width: progressPercent + '%' }"></view>
              <view class="progress-thumb" :style="{ left: progressPercent + '%' }"></view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import NavBar from '@/components/NavBar.vue'

const meditationId = ref('')
const meditationTitle = ref('冥想练习')

// 音频播放相关状态
const audioContext = ref<UniApp.InnerAudioContext | null>(null)
const backgroundAudioManager = ref<UniApp.BackgroundAudioManager | null>(null)
const isPlaying = ref(false)
const currentTime = ref(0)
const duration = ref(0)
const progressPercent = ref(0)
const updateTimer = ref<number | null>(null)
const currentPathIndex = ref(0)
const useBackgroundAudio = ref(false)

// 可能的音频路径
const possiblePaths = [
  '/static/sound/sound-1.mp3',
  'static/sound/sound-1.mp3',
  './static/sound/sound-1.mp3',
  '../../static/sound/sound-1.mp3'
]

// 尝试下一个路径
const tryNextPath = () => {
  currentPathIndex.value++
  if (currentPathIndex.value < possiblePaths.length) {
    console.log('尝试下一个路径:', possiblePaths[currentPathIndex.value])
    audioContext.value!.src = possiblePaths[currentPathIndex.value]
    return true
  }
  return false
}

// 初始化音频
const initAudio = () => {
  // 如果已有音频上下文，先销毁
  if (audioContext.value) {
    audioContext.value.destroy()
  }

  audioContext.value = uni.createInnerAudioContext()

  const audioSrc = possiblePaths[currentPathIndex.value]
  audioContext.value.src = audioSrc
  audioContext.value.autoplay = false
  audioContext.value.loop = false
  audioContext.value.obeyMuteSwitch = false // 不遵循静音开关
  audioContext.value.volume = 1.0 // 设置音量为最大

  console.log('初始化音频，路径:', audioSrc)
  console.log('音频上下文:', audioContext.value)

  // 监听音频事件
  audioContext.value.onCanplay(() => {
    console.log('音频可以播放了')
    duration.value = audioContext.value?.duration || 0
    console.log('音频总时长:', duration.value)
  })

  audioContext.value.onTimeUpdate(() => {
    if (audioContext.value) {
      currentTime.value = audioContext.value.currentTime
      progressPercent.value = duration.value > 0 ? (currentTime.value / duration.value) * 100 : 0
      console.log('时间更新:', currentTime.value, '/', duration.value, '进度:', progressPercent.value + '%')
    }
  })

  audioContext.value.onPlay(() => {
    console.log('音频开始播放事件触发')
    isPlaying.value = true
    startProgressUpdate()
  })

  audioContext.value.onPause(() => {
    console.log('音频暂停事件触发')
    isPlaying.value = false
    stopProgressUpdate()
  })

  audioContext.value.onStop(() => {
    console.log('音频停止事件触发')
    isPlaying.value = false
    stopProgressUpdate()
  })

  audioContext.value.onEnded(() => {
    console.log('音频播放完成')
    isPlaying.value = false
    currentTime.value = 0
    progressPercent.value = 0
    // 播放完成后跳转到完成页面
    uni.navigateTo({
      url: '/pages/meditation-complete/meditation-complete?id=' + meditationId.value
    })
  })

  audioContext.value.onError((error) => {
    console.error('音频播放错误:', error)
    console.error('错误详情:', JSON.stringify(error))
    console.error('当前路径:', audioContext.value?.src)

    // 尝试下一个路径
    if (tryNextPath()) {
      console.log('尝试新路径，重新初始化音频')
      // 重新初始化音频
      setTimeout(() => {
        initAudio()
      }, 100)
    } else {
      console.error('所有路径都尝试失败')
      uni.showToast({
        title: '音频文件无法加载',
        icon: 'none',
        duration: 3000
      })
    }
  })

  audioContext.value.onWaiting(() => {
    console.log('音频缓冲中...')
  })

  audioContext.value.onSeeked(() => {
    console.log('音频跳转完成')
  })
}

// 手动更新进度
const startProgressUpdate = () => {
  stopProgressUpdate() // 先清除之前的定时器
  console.log('开始手动更新进度')
  updateTimer.value = setInterval(() => {
    if (audioContext.value && isPlaying.value) {
      const newCurrentTime = audioContext.value.currentTime || 0
      const newDuration = audioContext.value.duration || duration.value

      currentTime.value = newCurrentTime
      if (newDuration > 0) {
        duration.value = newDuration
        progressPercent.value = (newCurrentTime / newDuration) * 100
      }

      console.log('手动更新进度:', newCurrentTime.toFixed(2), '/', newDuration.toFixed(2), '进度:', progressPercent.value.toFixed(1) + '%')

      // 检查是否播放完成
      if (newCurrentTime >= newDuration && newDuration > 0) {
        console.log('音频播放完成（手动检测）')
        isPlaying.value = false
        stopProgressUpdate()
        // 跳转到完成页面
        uni.navigateTo({
          url: '/pages/meditation-complete/meditation-complete?id=' + meditationId.value
        })
      }
    }
  }, 200) // 每200ms更新一次
}

const stopProgressUpdate = () => {
  if (updateTimer.value) {
    clearInterval(updateTimer.value)
    updateTimer.value = null
  }
}

// 播放/暂停切换
const togglePlay = () => {
  if (useBackgroundAudio.value) {
    toggleBackgroundAudio()
    return
  }

  if (!audioContext.value) {
    console.log('音频上下文不存在')
    return
  }

  console.log('当前播放状态:', isPlaying.value)
  console.log('音频源:', audioContext.value.src)
  console.log('音频时长:', audioContext.value.duration)
  console.log('音频当前时间:', audioContext.value.currentTime)

  if (isPlaying.value) {
    console.log('暂停音频')
    audioContext.value.pause()
    // 手动设置状态，以防事件不触发
    isPlaying.value = false
    stopProgressUpdate()
  } else {
    console.log('开始播放音频')

    // 尝试播放前先检查音频状态
    if (audioContext.value.duration === 0) {
      console.log('音频时长为0，尝试重新加载')
      audioContext.value.src = audioContext.value.src // 重新设置源
    }

    try {
      // 在小程序中，可能需要先停止再播放
      audioContext.value.stop()
      audioContext.value.seek(0)

      // 延迟一下再播放，确保停止命令执行完毕
      setTimeout(() => {
        audioContext.value!.play()
        console.log('播放命令已发送')

        // 延迟检查播放状态
        setTimeout(() => {
          console.log('播放后状态检查:')
          console.log('- 当前时间:', audioContext.value?.currentTime)
          console.log('- 是否暂停:', audioContext.value?.paused)
          console.log('- 音频时长:', audioContext.value?.duration)

          // 如果还是暂停状态，强制设置状态
          if (audioContext.value?.paused) {
            console.log('音频仍然暂停，可能是小程序限制')
            isPlaying.value = false
            stopProgressUpdate()
            uni.showToast({
              title: '音频播放受限，尝试点击"背景音频"按钮',
              icon: 'none',
              duration: 3000
            })
          } else {
            console.log('音频播放成功')
            isPlaying.value = true
            startProgressUpdate()
          }
        }, 500)
      }, 100)

    } catch (error) {
      console.error('播放失败:', error)
      isPlaying.value = false
      uni.showToast({
        title: '播放失败',
        icon: 'none'
      })
    }
  }
}

// 背景音频播放切换
const toggleBackgroundAudio = () => {
  if (!backgroundAudioManager.value) {
    console.log('背景音频管理器不存在')
    return
  }

  console.log('背景音频播放状态:', isPlaying.value)

  if (isPlaying.value) {
    console.log('暂停背景音频')
    backgroundAudioManager.value.pause()
  } else {
    console.log('播放背景音频')
    backgroundAudioManager.value.play()
  }
}

// 显示音频播放提示
const showAudioTips = () => {
  uni.showModal({
    title: '音频播放问题排查',
    content: `请检查以下设置：
1. 设备音量是否开启
2. 小程序是否有音频权限
3. 是否开启了静音模式
4. 尝试重启小程序
5. 在微信中：设置 > 通用 > 功能 > 小程序 > 允许音频播放

当前状态：
- 音频时长：${duration.value.toFixed(1)}秒
- 播放状态：${isPlaying.value ? '播放中' : '暂停'}
- 使用背景音频：${useBackgroundAudio.value ? '是' : '否'}`,
    showCancel: false,
    confirmText: '知道了'
  })
}

// 格式化时间显示
const formatTime = (seconds: number): string => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 测试方法
const testNextPath = () => {
  if (tryNextPath()) {
    console.log('切换到路径:', possiblePaths[currentPathIndex.value])
    initAudio()
  } else {
    console.log('已经是最后一个路径')
    currentPathIndex.value = 0
    initAudio()
  }
}

const checkAudioStatus = () => {
  if (audioContext.value) {
    console.log('=== 音频状态检查 ===')
    console.log('源路径:', audioContext.value.src)
    console.log('当前时间:', audioContext.value.currentTime)
    console.log('总时长:', audioContext.value.duration)
    console.log('是否暂停:', audioContext.value.paused)
    console.log('音量:', audioContext.value.volume)
    console.log('播放状态:', isPlaying.value)
    console.log('==================')
  }
}

const forcePlay = () => {
  if (!audioContext.value) return

  console.log('=== 强制播放测试 ===')

  // 尝试多种播放方式
  console.log('1. 直接播放')
  audioContext.value.play()

  setTimeout(() => {
    console.log('播放后状态:', audioContext.value?.paused)
    if (audioContext.value?.paused) {
      console.log('2. 尝试先停止再播放')
      audioContext.value.stop()
      setTimeout(() => {
        audioContext.value!.play()
        setTimeout(() => {
          console.log('第二次播放后状态:', audioContext.value?.paused)
          if (audioContext.value?.paused) {
            console.log('3. 尝试重新设置源')
            const currentSrc = audioContext.value.src
            audioContext.value.src = ''
            setTimeout(() => {
              audioContext.value!.src = currentSrc
              setTimeout(() => {
                audioContext.value!.play()
                setTimeout(() => {
                  console.log('第三次播放后状态:', audioContext.value?.paused)
                }, 500)
              }, 100)
            }, 100)
          }
        }, 500)
      }, 100)
    }
  }, 500)
}

const resetAudio = () => {
  console.log('重置音频')
  stopProgressUpdate()
  isPlaying.value = false
  currentTime.value = 0
  progressPercent.value = 0
  if (useBackgroundAudio.value) {
    initBackgroundAudio()
  } else {
    initAudio()
  }
}

// 尝试使用背景音频
const tryBackgroundAudio = () => {
  console.log('尝试使用背景音频管理器')
  useBackgroundAudio.value = true
  initBackgroundAudio()
}

// 初始化背景音频
const initBackgroundAudio = () => {
  try {
    backgroundAudioManager.value = uni.getBackgroundAudioManager()

    const audioSrc = possiblePaths[currentPathIndex.value]
    backgroundAudioManager.value.title = meditationTitle.value
    backgroundAudioManager.value.epname = '冥想音频'
    backgroundAudioManager.value.singer = '多肉冥想'
    backgroundAudioManager.value.coverImgUrl = ''
    backgroundAudioManager.value.webUrl = ''
    backgroundAudioManager.value.src = audioSrc

    console.log('背景音频初始化完成，路径:', audioSrc)

    // 监听背景音频事件
    backgroundAudioManager.value.onCanplay(() => {
      console.log('背景音频可以播放')
      duration.value = backgroundAudioManager.value?.duration || 0
    })

    backgroundAudioManager.value.onTimeUpdate(() => {
      if (backgroundAudioManager.value) {
        currentTime.value = backgroundAudioManager.value.currentTime || 0
        const dur = backgroundAudioManager.value.duration || duration.value
        if (dur > 0) {
          duration.value = dur
          progressPercent.value = (currentTime.value / dur) * 100
        }
        console.log('背景音频时间更新:', currentTime.value, '/', dur)
      }
    })

    backgroundAudioManager.value.onPlay(() => {
      console.log('背景音频开始播放')
      isPlaying.value = true
    })

    backgroundAudioManager.value.onPause(() => {
      console.log('背景音频暂停')
      isPlaying.value = false
    })

    backgroundAudioManager.value.onEnded(() => {
      console.log('背景音频播放完成')
      isPlaying.value = false
      uni.navigateTo({
        url: '/pages/meditation-complete/meditation-complete?id=' + meditationId.value
      })
    })

    backgroundAudioManager.value.onError((error) => {
      console.error('背景音频错误:', error)
      uni.showToast({
        title: '背景音频播放失败',
        icon: 'none'
      })
    })

  } catch (error) {
    console.error('背景音频管理器初始化失败:', error)
    uni.showToast({
      title: '不支持背景音频',
      icon: 'none'
    })
  }
}

// 进度条点击事件
const onProgressClick = (event: any) => {
  if (!audioContext.value || duration.value === 0) {
    console.log('无法跳转：音频未准备好或时长为0')
    return
  }

  console.log('进度条点击事件:', event)

  // 使用 uni.createSelectorQuery 获取元素信息
  const query = uni.createSelectorQuery()
  query.select('.progress-track').boundingClientRect((rect: any) => {
    if (rect) {
      const clickX = event.detail.x - rect.left
      const percent = Math.max(0, Math.min(1, clickX / rect.width))
      const targetTime = percent * duration.value

      console.log('跳转到时间:', targetTime, '进度:', percent * 100 + '%')

      audioContext.value?.seek(targetTime)
      currentTime.value = targetTime
      progressPercent.value = percent * 100
    }
  }).exec()
}

onMounted(() => {
  // 获取路由参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options

  if (options && options.id) {
    meditationId.value = options.id
    // 根据id设置标题（这里可以从服务器获取）
    const titleMap: Record<string, string> = {
      '101': '呼吸觉察冥想',
      '201': '深度放松冥想',
      '301': '单点专注冥想'
    }
    meditationTitle.value = titleMap[options.id] || '冥想练习'
  }

  // 初始化音频
  initAudio()
})

onUnmounted(() => {
  // 清理定时器
  stopProgressUpdate()

  // 清理音频资源
  if (audioContext.value) {
    audioContext.value.destroy()
  }
})
</script>

<style scoped>
.meditation-page {
  min-height: 100vh;
  background: linear-gradient(180deg, #F0F9F0 0%, #F7FAFC 40%, #FFFFFF 100%);
  display: flex;
  flex-direction: column;
  position: relative;
}

.meditation-page::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.08) 0%, rgba(168, 208, 141, 0.05) 50%, rgba(240, 249, 240, 0.03) 100%);
  pointer-events: none;
  z-index: 0;
}

.meditation-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 176rpx 32rpx 32rpx;
  position: relative;
  z-index: 1;
}

/* 冥想信息 */
.meditation-info {
  text-align: center;
  margin-bottom: 120rpx;
}

.meditation-text {
  font-size: 48rpx;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 16rpx;
  letter-spacing: 1rpx;
}

.meditation-subtitle {
  font-size: 28rpx;
  color: #7fb069;
  font-weight: 500;
}

/* 音频播放器 */
.audio-player {
  width: 100%;
  max-width: 600rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(247, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20rpx);
  border-radius: 32rpx;
  padding: 60rpx 40rpx;
  box-shadow: 0 12rpx 40rpx rgba(127, 176, 105, 0.12), 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow: hidden;
}

.audio-player::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(127, 176, 105, 0.02) 0%, rgba(168, 208, 141, 0.01) 100%);
  pointer-events: none;
}

/* 播放按钮 */
.play-button {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  background: linear-gradient(135deg, #7FB069 0%, #A8D08D 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 60rpx;
  box-shadow: 0 8rpx 24rpx rgba(127, 176, 105, 0.3), 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
  z-index: 1;
}

.play-button:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(127, 176, 105, 0.4), 0 1rpx 4rpx rgba(0, 0, 0, 0.15);
}

.play-icon {
  font-size: 48rpx;
  color: #ffffff;
  font-weight: bold;
  margin-left: 4rpx;
}

/* 进度区域 */
.progress-section {
  width: 100%;
  position: relative;
  z-index: 1;
}

.time-display {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.current-time,
.total-time {
  font-size: 28rpx;
  color: #4a5568;
  font-weight: 600;
  font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
}

/* 进度条 */
.progress-bar {
  width: 100%;
  padding: 20rpx 0;
  cursor: pointer;
}

.progress-track {
  width: 100%;
  height: 8rpx;
  background: rgba(160, 174, 192, 0.2);
  border-radius: 4rpx;
  position: relative;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #7FB069 0%, #A8D08D 100%);
  border-radius: 4rpx;
  transition: width 0.1s ease;
  position: relative;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  border-radius: 4rpx;
}

.progress-thumb {
  position: absolute;
  top: 50%;
  width: 20rpx;
  height: 20rpx;
  background: #7FB069;
  border: 3rpx solid #ffffff;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 2rpx 8rpx rgba(127, 176, 105, 0.4);
  transition: left 0.1s ease;
}
</style>